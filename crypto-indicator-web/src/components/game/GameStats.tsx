/**
 * Game Stats Component
 *
 * Displays current game statistics, score, and timer
 * Appears in the header during game mode
 */

import React from 'react';

import { motion } from 'framer-motion';

import { useGameState } from '../../context/GameContext';

// Helper function to check if in round
const isInRound = (currentRound: any): boolean => {
  return Boolean(currentRound && currentRound.state === 'predicting');
};

export const GameStats: React.FC = () => {
  const { currentSession, currentRound } = useGameState();

  if (!currentSession) {return null;}

  const stats = currentSession.stats;
  const inRound = isInRound(currentRound);

  return (
    <motion.div
      className="game-stats"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="stats-container">
        <div className="stat-item score">
          <span className="stat-label">Score</span>
          <motion.span
            className="stat-value"
            key={stats.totalPoints}
            initial={{ scale: 1.2 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            {stats.totalPoints.toLocaleString()}
          </motion.span>
        </div>

        <div className="stat-item accuracy">
          <span className="stat-label">Accuracy</span>
          <span className="stat-value">
            {stats.totalRounds > 0 ? `${stats.accuracy.toFixed(1)}%` : '0%'}
          </span>
        </div>

        <div className="stat-item streak">
          <span className="stat-label">Streak</span>
          <motion.span
            className="stat-value"
            key={stats.currentStreak}
            animate={{
              scale: stats.currentStreak > 0 ? [1, 1.1, 1] : 1,
              color: stats.currentStreak > 2 ? 'var(--accent-yellow)' : 'var(--text-primary)',
            }}
            transition={{ duration: 0.3 }}
          >
            {stats.currentStreak}
            {stats.currentStreak > 2 && '🔥'}
          </motion.span>
        </div>

        <div className="stat-item rounds">
          <span className="stat-label">Round</span>
          <span className="stat-value">
            {currentSession.rounds.length}/20
          </span>
        </div>

        {inRound && (
          <motion.div
            className="stat-item timer"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <span className="stat-label">Time</span>
            <span className="stat-value timer-value">
              {Math.ceil(currentRound.timeRemaining)}s
            </span>
          </motion.div>
        )}
      </div>

      {stats.currentStreak > 0 && (
        <motion.div
          className="streak-indicator"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
        >
          <span className="streak-text">
            {stats.currentStreak} in a row! 
            {stats.currentStreak >= 5 && ' Amazing!'}
            {stats.currentStreak >= 10 && ' Incredible!'}
          </span>
        </motion.div>
      )}
    </motion.div>
  );
};

// Compact version for mobile
export const GameStatsMobile: React.FC = () => {
  const { currentSession, currentRound } = useGameState();

  if (!currentSession) {return null;}

  const { stats } = currentSession;

  return (
    <motion.div
      className="game-stats-mobile"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="mobile-stats-row">
        <div className="mobile-stat">
          <span className="mobile-stat-value">{stats.totalPoints.toLocaleString()}</span>
          <span className="mobile-stat-label">Score</span>
        </div>
        
        <div className="mobile-stat">
          <span className="mobile-stat-value">
            {stats.totalRounds > 0 ? `${stats.accuracy.toFixed(0)}%` : '0%'}
          </span>
          <span className="mobile-stat-label">Accuracy</span>
        </div>
        
        <div className="mobile-stat">
          <span className="mobile-stat-value">
            {stats.currentStreak}{stats.currentStreak > 2 ? '🔥' : ''}
          </span>
          <span className="mobile-stat-label">Streak</span>
        </div>

        <div className="mobile-stat">
          <span className="mobile-stat-value">
            {currentSession.rounds.length}/20
          </span>
          <span className="mobile-stat-label">Round</span>
        </div>
      </div>

      {currentRound && currentRound.state === 'predicting' && (
        <motion.div
          className="mobile-timer"
          animate={{
            scale: currentRound.timeRemaining <= 5 ? [1, 1.05, 1] : 1,
          }}
          transition={{ duration: 0.5, repeat: Infinity }}
        >
          <span className="timer-label">Time:</span>
          <span className="timer-value">
            {Math.ceil(currentRound.timeRemaining)}s
          </span>
        </motion.div>
      )}
    </motion.div>
  );
};

// Game over summary
interface GameSummaryProps {
  isVisible: boolean;
  onClose: () => void;
  onPlayAgain: () => void;
}

export const GameSummary: React.FC<GameSummaryProps> = ({
  isVisible,
  onClose,
  onPlayAgain,
}) => {
  const { currentSession, allTimeStats } = useGameState();

  if (!isVisible || !currentSession) {return null;}

  const { stats } = currentSession;
  const isNewBest = stats.totalPoints > allTimeStats.totalPoints;

  return (
    <motion.div
      className="game-summary-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="game-summary"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
      >
        <div className="summary-header">
          <h2>Game Complete!</h2>
          {isNewBest && (
            <motion.div
              className="new-best-badge"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: 'spring' }}
            >
              🏆 New Best Score!
            </motion.div>
          )}
        </div>

        <div className="summary-stats">
          <div className="summary-stat-item">
            <span className="summary-stat-label">Final Score</span>
            <span className="summary-stat-value">{stats.totalPoints.toLocaleString()}</span>
          </div>
          
          <div className="summary-stat-item">
            <span className="summary-stat-label">Accuracy</span>
            <span className="summary-stat-value">{stats.accuracy.toFixed(1)}%</span>
          </div>
          
          <div className="summary-stat-item">
            <span className="summary-stat-label">Correct Predictions</span>
            <span className="summary-stat-value">{stats.correctPredictions}/{stats.totalRounds}</span>
          </div>
          
          <div className="summary-stat-item">
            <span className="summary-stat-label">Best Streak</span>
            <span className="summary-stat-value">{stats.bestStreak}</span>
          </div>
          
          <div className="summary-stat-item">
            <span className="summary-stat-label">Average Time</span>
            <span className="summary-stat-value">{(stats.averageTime / 1000).toFixed(1)}s</span>
          </div>
        </div>

        <div className="summary-actions">
          <button
            className="summary-button play-again"
            onClick={onPlayAgain}
          >
            Play Again
          </button>
          <button
            className="summary-button close"
            onClick={onClose}
          >
            Back to Dashboard
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
};
