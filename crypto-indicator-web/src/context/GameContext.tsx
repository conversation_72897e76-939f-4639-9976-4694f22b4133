/**
 * Game Context
 * 
 * React context for managing Signal Prediction Challenge game state
 * Uses Zustand for state management with React integration
 */

import React, { createContext, ReactNode,useContext } from 'react';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { GameService } from '../services/GameService';

import type {
  DEFAULT_GAME_CONFIG,
  GameActions,
  GameMode,
  GameRound,
  GameSession,
  GameState,
  GameStats,
  LeaderboardEntry,
  SignalColor,
} from '../types/game';

// Game service instance
const gameService = new GameService(DEFAULT_GAME_CONFIG);

// Zustand store for game state
interface GameStore extends GameState, GameActions {}

const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      // Initial state
      mode: 'normal',
      currentSession: null,
      currentRound: null,
      allTimeStats: gameService.createEmptyStats(),
      leaderboard: [],
      isLoading: false,
      error: null,

      // Actions
      startGame: () => {
        const session = gameService.createSession();
        set({
          mode: 'game',
          currentSession: session,
          currentRound: null,
          isLoading: false,
          error: null,
        });
      },

      endGame: () => {
        const { currentSession, allTimeStats, leaderboard } = get();
        
        if (!currentSession) {return;}

        const finalizedSession = gameService.finalizeSession(currentSession);
        // Update all-time stats by processing each prediction
        let updatedAllTimeStats = allTimeStats;
        for (const round of finalizedSession.rounds) {
          if (round.prediction) {
            updatedAllTimeStats = gameService.updateStats(updatedAllTimeStats, round.prediction);
          }
        }

        // Add to leaderboard if score is good enough
        const leaderboardEntry = gameService.createLeaderboardEntry(finalizedSession);
        const updatedLeaderboard = gameService.sortLeaderboard([
          ...leaderboard,
          leaderboardEntry,
        ]).slice(0, 10); // Keep top 10

        set({
          mode: 'normal',
          currentSession: null,
          currentRound: null,
          allTimeStats: updatedAllTimeStats,
          leaderboard: updatedLeaderboard,
        });
      },

      startRound: (symbol: string, currency: string, actualSignal: SignalColor) => {
        const { currentSession } = get();
        
        if (!currentSession || !currentSession.isActive) {return;}

        const round = gameService.createRound(symbol, currency, actualSignal);
        
        set({
          currentRound: round,
          currentSession: {
            ...currentSession,
            rounds: [...currentSession.rounds, round],
          },
        });

        // Auto-skip round after time limit
        setTimeout(() => {
          const { currentRound: currentRoundCheck } = get();
          if (currentRoundCheck?.id === round.id && currentRoundCheck.state === 'predicting') {
            get().skipRound();
          }
        }, DEFAULT_GAME_CONFIG.roundTimeLimit * 1000);
      },

      makePrediction: (predictedSignal: SignalColor) => {
        const { currentRound, currentSession } = get();
        
        if (!currentRound || !currentSession || currentRound.state !== 'predicting') {return;}

        const { prediction, updatedStats } = gameService.processPrediction(
          currentRound,
          predictedSignal,
          currentSession.stats
        );

        const updatedRound: GameRound = {
          ...currentRound,
          state: 'revealed',
          prediction,
        };

        const updatedSession: GameSession = {
          ...currentSession,
          stats: updatedStats,
          rounds: currentSession.rounds.map(round =>
            round.id === currentRound.id ? updatedRound : round
          ),
        };

        set({
          currentRound: updatedRound,
          currentSession: updatedSession,
        });

        // Auto-complete round after showing result
        setTimeout(() => {
          const { currentRound: currentRoundCheck } = get();
          if (currentRoundCheck?.id === updatedRound.id) {
            set({
              currentRound: {
                ...currentRoundCheck,
                state: 'completed',
              },
            });

            // Check if session should end
            if (gameService.shouldEndSession(updatedSession)) {
              setTimeout(() => { get().endGame(); }, 1000);
            }
          }
        }, 3000); // Show result for 3 seconds
      },

      skipRound: () => {
        const { currentRound } = get();
        
        if (!currentRound || currentRound.state !== 'predicting') {return;}

        set({
          currentRound: {
            ...currentRound,
            state: 'completed',
          },
        });
      },

      resetStats: () => {
        set({
          allTimeStats: gameService.createEmptyStats(),
          leaderboard: [],
        });
      },

      setMode: (mode: GameMode) => {
        if (mode === 'normal') {
          get().endGame();
        }
        set({ mode });
      },
    }),
    {
      name: 'signal-prediction-game',
      partialize: (state) => ({
        allTimeStats: state.allTimeStats,
        leaderboard: state.leaderboard,
      }),
    }
  )
);

// React context for game store
const GameContext = createContext<GameStore | null>(null);

interface GameProviderProps {
  children: ReactNode;
}

/**
 * Game Context Provider
 * Provides game state and actions to child components
 */
export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const gameStore = useGameStore();

  return (
    <GameContext.Provider value={gameStore}>
      {children}
    </GameContext.Provider>
  );
};

/**
 * Hook to access game state and actions
 * Throws error if used outside of GameProvider
 */
export const useGame = (): GameStore => {
  const context = useContext(GameContext);
  
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  
  return context;
};

/**
 * Hook to access only game state (for read-only components)
 */
export const useGameState = (): GameState => {
  const game = useGame();
  
  return {
    mode: game.mode,
    currentSession: game.currentSession,
    currentRound: game.currentRound,
    allTimeStats: game.allTimeStats,
    leaderboard: game.leaderboard,
    isLoading: game.isLoading,
    error: game.error,
  };
};

/**
 * Hook to access only game actions
 */
export const useGameActions = (): GameActions => {
  const game = useGame();
  
  return {
    startGame: game.startGame,
    endGame: game.endGame,
    startRound: game.startRound,
    makePrediction: game.makePrediction,
    skipRound: game.skipRound,
    resetStats: game.resetStats,
    setMode: game.setMode,
  };
};
