/**
 * Game Toggle Component
 * 
 * Toggle button for switching between normal and game modes
 * Appears in the dashboard header
 */

import React from 'react';

import { motion } from 'framer-motion';

import { useGame } from '../../context/GameContext';

export const GameToggle: React.FC = () => {
  const { mode, startGame, endGame, currentSession } = useGame();

  const isGameMode = mode === 'game';
  const isGameActive = currentSession?.isActive;

  const handleToggle = () => {
    if (isGameMode) {
      endGame();
    } else {
      startGame();
    }
  };

  return (
    <div className="game-toggle-container">
      <motion.button
        className={`game-toggle ${isGameMode ? 'active' : ''}`}
        onClick={handleToggle}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        disabled={isGameActive && isGameMode}
      >
        <motion.div
          className="toggle-icon"
          animate={{
            rotate: isGameMode ? 360 : 0,
          }}
          transition={{ duration: 0.3 }}
        >
          {isGameMode ? '🎯' : '🎮'}
        </motion.div>
        
        <span className="toggle-label">
          {isGameMode ? 'Game Mode' : 'Play Game'}
        </span>

        {isGameMode && (
          <motion.div
            className="game-indicator"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
          >
            <div className="indicator-dot" />
          </motion.div>
        )}
      </motion.button>

      {isGameMode && (
        <motion.div
          className="game-status"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
        >
          <span className="status-text">
            {isGameActive ? 'Game Active' : 'Game Ready'}
          </span>
        </motion.div>
      )}
    </div>
  );
};
