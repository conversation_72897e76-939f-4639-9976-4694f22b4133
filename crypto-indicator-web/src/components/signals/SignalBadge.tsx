import React, { useEffect,useState } from 'react';

import { useGame } from '../../context/GameContext';
import { buildSignalClassName, getDisplayTitle,getSignalInfo } from '../../utils/signalHelpers';
import { PredictionOverlay, PredictionResult } from '../game/PredictionOverlay';

import type { SignalColor } from '../../types/game';

// Helper functions
const isValidSignalColor = (color: string): color is SignalColor => {
  return ['gold', 'blue', 'gray'].includes(color);
};

const shouldShowGamePrediction = (
  isGameMode: boolean,
  currentRound: any,
  symbol: string | undefined
): boolean => {
  return Boolean(
    isGameMode &&
    currentRound?.state === 'predicting' &&
    currentRound?.symbol === symbol
  );
};

const shouldStartGameRound = (
  isGameMode: boolean,
  symbol: string | undefined,
  currency: string | undefined,
  color: string | undefined,
  currentRound: any
): boolean => {
  return Boolean(
    isGameMode &&
    symbol &&
    currency &&
    color &&
    !currentRound &&
    isValidSignalColor(color.toLowerCase())
  );
};

const getSignalIcon = (
  loading: boolean,
  isGameMode: boolean,
  currentRound: any,
  signalInfo: any
): string => {
  if (loading) {
    return '⟳';
  }

  if (isGameMode && currentRound?.state === 'predicting') {
    return '❓';
  }

  return signalInfo.icon;
};

const getSignalLabel = (
  isGameMode: boolean,
  currentRound: any,
  signalInfo: any
): string => {
  if (isGameMode && currentRound?.state === 'predicting') {
    return 'Predict';
  }

  return signalInfo.label;
};

// Component-specific styles
import '../../styles/components/signal-badge.css';
import '../../styles/components/game.css';

interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
  symbol?: string;
  currency?: string;
}

export const SignalBadge: React.FC<SignalBadgeProps> = ({
  color,
  onClick,
  clickable = false,
  title,
  loading = false,
  disabled = false,
  symbol,
  currency
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const { mode, currentRound, startRound } = useGame();
  const [showPredictionOverlay, setShowPredictionOverlay] = useState(false);
  const [showResult, setShowResult] = useState(false);

  const signalInfo = getSignalInfo(color);
  const isGameMode = mode === 'game';
  const isInteractive = clickable && !disabled && !loading;
  const className = buildSignalClassName(signalInfo, isInteractive, loading, disabled);
  const displayTitle = getDisplayTitle(title, isInteractive, signalInfo);

  // Game mode logic
  useEffect(() => {
    if (shouldStartGameRound(isGameMode, symbol, currency, color, currentRound)) {
      const actualSignal = color!.toLowerCase() as SignalColor;
      startRound(symbol!, currency!, actualSignal);
      setShowPredictionOverlay(true);
    }
  }, [isGameMode, symbol, currency, color, currentRound, startRound]);

  // Show result when round is revealed
  useEffect(() => {
    if (currentRound && currentRound.state === 'revealed' && currentRound.symbol === symbol) {
      setShowPredictionOverlay(false);
      setShowResult(true);

      // Hide result after 3 seconds
      const timer = setTimeout(() => {
        setShowResult(false);
      }, 3000);

      return () => { clearTimeout(timer); };
    }
  }, [currentRound, symbol]);

  const handleClick = () => {
    if (isGameMode && shouldStartGameRound(isGameMode, symbol, currency, color, null)) {
      const actualSignal = color!.toLowerCase() as SignalColor;
      startRound(symbol!, currency!, actualSignal);
      setShowPredictionOverlay(true);
    } else if (isInteractive && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isInteractive && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      onClick?.();
    }
  };

  return (
    <>
      <span
        className={className}
        onClick={handleClick}
        onMouseEnter={() => { setShowTooltip(true); }}
        onMouseLeave={() => { setShowTooltip(false); }}
        role={isInteractive || isGameMode ? 'button' : 'status'}
        tabIndex={isInteractive || isGameMode ? 0 : undefined}
        onKeyDown={handleKeyDown}
        aria-label={isGameMode ? 'Make prediction' : (isInteractive ? signalInfo.ariaLabel : signalInfo.description)}
        aria-disabled={disabled}
        aria-busy={loading}
      >
        <span className={loading ? "signal-spinner" : "signal-icon"} aria-hidden="true">
          {getSignalIcon(loading, isGameMode, currentRound, signalInfo)}
        </span>
        <span className="signal-label">
          {getSignalLabel(isGameMode, currentRound, signalInfo)}
        </span>

        {displayTitle && !isGameMode && (
          <div className={`signal-badge-tooltip ${showTooltip ? 'visible' : ''}`} role="tooltip">
            {displayTitle}
          </div>
        )}
      </span>

      {/* Game Mode Overlays */}
      {shouldStartGameRound(isGameMode, symbol, currency, color, null) && (
        <>
          <PredictionOverlay
            symbol={symbol}
            currency={currency}
            actualSignal={color.toLowerCase() as SignalColor}
            isVisible={showPredictionOverlay}
            onPrediction={() => { setShowPredictionOverlay(false); }}
          />

          {currentRound?.prediction && showResult && (
            <PredictionResult
              prediction={currentRound.prediction.predictedSignal}
              actual={currentRound.prediction.actualSignal}
              isCorrect={currentRound.prediction.isCorrect}
              points={currentRound.prediction.points}
              timeToPredict={currentRound.prediction.timeToPredict}
              isVisible={showResult}
            />
          )}
        </>
      )}
    </>
  );
};
