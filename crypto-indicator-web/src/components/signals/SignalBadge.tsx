import React, { useEffect,useState } from 'react';

import { useGame } from '../../context/GameContext';
import { buildSignalClassName, getDisplayTitle,getSignalInfo } from '../../utils/signalHelpers';
import { PredictionOverlay, PredictionResult } from '../game/PredictionOverlay';

import type { SignalColor } from '../../types/game';

// Component-specific styles
import '../../styles/components/signal-badge.css';
import '../../styles/components/game.css';

/* eslint-disable max-lines-per-function, complexity, @typescript-eslint/strict-boolean-expressions, no-nested-ternary */

interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
  symbol?: string;
  currency?: string;
}

export const SignalBadge: React.FC<SignalBadgeProps> = ({
  color,
  onClick,
  clickable = false,
  title,
  loading = false,
  disabled = false,
  symbol,
  currency
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const { mode, currentRound, startRound } = useGame();
  const [showPredictionOverlay, setShowPredictionOverlay] = useState(false);
  const [showResult, setShowResult] = useState(false);

  const signalInfo = getSignalInfo(color);
  const isGameMode = mode === 'game';
  const isInteractive = clickable && !disabled && !loading;
  const className = buildSignalClassName(signalInfo, isInteractive, loading, disabled);
  const displayTitle = getDisplayTitle(title, isInteractive, signalInfo);

  // Game mode logic
  useEffect(() => {
    if (isGameMode && symbol && currency && color && !currentRound) {
      // Start a new round when in game mode and signal is available
      const actualSignal = color.toLowerCase() as SignalColor;
      if (['gold', 'blue', 'gray'].includes(actualSignal)) {
        startRound(symbol, currency, actualSignal);
        setShowPredictionOverlay(true);
      }
    }
  }, [isGameMode, symbol, currency, color, currentRound, startRound]);

  // Show result when round is revealed
  useEffect(() => {
    if (currentRound && currentRound.state === 'revealed' && currentRound.symbol === symbol) {
      setShowPredictionOverlay(false);
      setShowResult(true);

      // Hide result after 3 seconds
      const timer = setTimeout(() => {
        setShowResult(false);
      }, 3000);

      return () => { clearTimeout(timer); };
    }
  }, [currentRound, symbol]);

  const handleClick = () => {
    if (isGameMode) {
      // In game mode, clicking shows prediction overlay
      if (symbol && currency && color) {
        const actualSignal = color.toLowerCase() as SignalColor;
        if (['gold', 'blue', 'gray'].includes(actualSignal)) {
          startRound(symbol, currency, actualSignal);
          setShowPredictionOverlay(true);
        }
      }
    } else if (isInteractive && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isInteractive && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      onClick?.();
    }
  };

  return (
    <>
      <span
        className={className}
        onClick={handleClick}
        onMouseEnter={() => { setShowTooltip(true); }}
        onMouseLeave={() => { setShowTooltip(false); }}
        role={isInteractive || isGameMode ? 'button' : 'status'}
        tabIndex={isInteractive || isGameMode ? 0 : undefined}
        onKeyDown={handleKeyDown}
        aria-label={isGameMode ? 'Make prediction' : (isInteractive ? signalInfo.ariaLabel : signalInfo.description)}
        aria-disabled={disabled}
        aria-busy={loading}
      >
        {loading ? (
          <span className="signal-spinner" aria-hidden="true">⟳</span>
        ) : (isGameMode && currentRound?.state === 'predicting' ? (
          <span className="signal-icon" aria-hidden="true">❓</span>
        ) : (
          <span className="signal-icon" aria-hidden="true">{signalInfo.icon}</span>
        ))}
        <span className="signal-label">
          {isGameMode && currentRound?.state === 'predicting' ? 'Predict' : signalInfo.label}
        </span>

        {displayTitle && !isGameMode && (
          <div className={`signal-badge-tooltip ${showTooltip ? 'visible' : ''}`} role="tooltip">
            {displayTitle}
          </div>
        )}
      </span>

      {/* Game Mode Overlays */}
      {isGameMode && symbol && currency && color && (
        <>
          <PredictionOverlay
            symbol={symbol}
            currency={currency}
            actualSignal={color.toLowerCase() as SignalColor}
            isVisible={showPredictionOverlay}
            onPrediction={() => { setShowPredictionOverlay(false); }}
          />

          {currentRound?.prediction && showResult && (
            <PredictionResult
              prediction={currentRound.prediction.predictedSignal}
              actual={currentRound.prediction.actualSignal}
              isCorrect={currentRound.prediction.isCorrect}
              points={currentRound.prediction.points}
              timeToPredict={currentRound.prediction.timeToPredict}
              isVisible={showResult}
            />
          )}
        </>
      )}
    </>
  );
};
