/**
 * Prediction Overlay Component
 *
 * Interactive overlay for making signal predictions in game mode
 * Appears over SignalBadge components during prediction phase
 */

import React, { useEffect,useState } from 'react';

import { AnimatePresence,motion } from 'framer-motion';

import { useGame } from '../../context/GameContext';

import type { SignalColor } from '../../types/game';

/* eslint-disable max-lines-per-function, @typescript-eslint/no-unused-vars, sonarjs/no-duplicate-string, @typescript-eslint/no-empty-function, unicorn/consistent-function-scoping */

interface PredictionOverlayProps {
  symbol: string;
  currency: string;
  actualSignal: SignalColor;
  isVisible: boolean;
  onPrediction?: (prediction: SignalColor) => void;
}

export const PredictionOverlay: React.FC<PredictionOverlayProps> = ({
  symbol,
  currency,
  actualSignal,
  isVisible,
  onPrediction,
}) => {
  const { currentRound, makePrediction } = useGame();
  const [timeRemaining, setTimeRemaining] = useState(30);
  const [hasStarted, setHasStarted] = useState(false);

  // Timer effect
  useEffect(() => {
    if (!isVisible || !currentRound || currentRound.state !== 'predicting') {
      return;
    }

    setHasStarted(true);
    const interval = setInterval(() => {
      const elapsed = (Date.now() - currentRound.startTime) / 1000;
      const remaining = Math.max(0, 30 - elapsed);
      setTimeRemaining(remaining);

      if (remaining <= 0) {
        clearInterval(interval);
      }
    }, 100);

    return () => { clearInterval(interval); };
  }, [isVisible, currentRound]);

  const handlePrediction = (prediction: SignalColor) => {
    makePrediction(prediction);
    onPrediction?.(prediction);
  };

  const getTimerColor = () => {
    if (timeRemaining > 20) {return 'var(--accent-teal)';}
    if (timeRemaining > 10) {return 'var(--accent-yellow)';}
    return 'var(--accent-red)';
  };

  const predictionButtons = [
    {
      signal: 'gold' as SignalColor,
      label: 'Bullish',
      icon: '▲',
      color: 'var(--accent-yellow)',
      description: 'Predict upward trend',
    },
    {
      signal: 'blue' as SignalColor,
      label: 'Bearish',
      icon: '▼',
      color: 'var(--accent-blue)',
      description: 'Predict downward trend',
    },
    {
      signal: 'gray' as SignalColor,
      label: 'Neutral',
      icon: '●',
      color: 'var(--text-muted)',
      description: 'Predict sideways movement',
    },
  ];

  if (!isVisible || !currentRound) {return null;}

  return (
    <AnimatePresence>
      <motion.div
        className="prediction-overlay"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      >
        <div className="prediction-content">
          <div className="prediction-header">
            <h3 className="prediction-title">
              Predict Signal for {symbol}/{currency}
            </h3>
            <div 
              className="prediction-timer"
              style={{ color: getTimerColor() }}
            >
              <motion.div
                className="timer-circle"
                initial={{ scale: 1 }}
                animate={{ 
                  scale: timeRemaining <= 5 ? [1, 1.1, 1] : 1,
                }}
                transition={{ 
                  duration: 0.5,
                  repeat: timeRemaining <= 5 ? Infinity : 0,
                }}
              >
                {Math.ceil(timeRemaining)}s
              </motion.div>
            </div>
          </div>

          <div className="prediction-buttons">
            {predictionButtons.map((button) => (
              <motion.button
                key={button.signal}
                className="prediction-button"
                onClick={() => { handlePrediction(button.signal); }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                style={{
                  '--button-color': button.color,
                } as React.CSSProperties}
              >
                <span className="prediction-icon">{button.icon}</span>
                <span className="prediction-label">{button.label}</span>
                <span className="prediction-description">{button.description}</span>
              </motion.button>
            ))}
          </div>

          <div className="prediction-progress">
            <motion.div
              className="progress-bar"
              initial={{ width: '100%' }}
              animate={{ width: `${(timeRemaining / 30) * 100}%` }}
              style={{ backgroundColor: getTimerColor() }}
              transition={{ duration: 0.1 }}
            />
          </div>

          <div className="prediction-hint">
            <p>Make your prediction based on the chart patterns and indicators!</p>
          </div>
        </div>

        <motion.div
          className="prediction-backdrop"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => {}} // Prevent closing on backdrop click during game
        />
      </motion.div>
    </AnimatePresence>
  );
};

// Results overlay for showing prediction results
interface PredictionResultProps {
  prediction: SignalColor;
  actual: SignalColor;
  isCorrect: boolean;
  points: number;
  timeToPredict: number;
  isVisible: boolean;
}

export const PredictionResult: React.FC<PredictionResultProps> = ({
  prediction,
  actual,
  isCorrect,
  points,
  timeToPredict,
  isVisible,
}) => {
  const getSignalInfo = (signal: SignalColor) => {
    switch (signal) {
      case 'gold': {
        return { label: 'Bullish', icon: '▲', color: 'var(--accent-yellow)' };
      }
      case 'blue': {
        return { label: 'Bearish', icon: '▼', color: 'var(--accent-blue)' };
      }
      case 'gray': {
        return { label: 'Neutral', icon: '●', color: 'var(--text-muted)' };
      }
    }
  };

  const predictionInfo = getSignalInfo(prediction);
  const actualInfo = getSignalInfo(actual);

  if (!isVisible) {return null;}

  return (
    <AnimatePresence>
      <motion.div
        className="prediction-result"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
      >
        <div className="result-content">
          <motion.div
            className={`result-status ${isCorrect ? 'correct' : 'incorrect'}`}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          >
            {isCorrect ? '✓' : '✗'}
          </motion.div>

          <div className="result-details">
            <div className="result-prediction">
              <span>Your prediction:</span>
              <span style={{ color: predictionInfo.color }}>
                {predictionInfo.icon} {predictionInfo.label}
              </span>
            </div>
            <div className="result-actual">
              <span>Actual signal:</span>
              <span style={{ color: actualInfo.color }}>
                {actualInfo.icon} {actualInfo.label}
              </span>
            </div>
          </div>

          <div className="result-score">
            <motion.div
              className="points-earned"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.4, type: 'spring' }}
            >
              +{points} points
            </motion.div>
            <div className="time-taken">
              Predicted in {(timeToPredict / 1000).toFixed(1)}s
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
